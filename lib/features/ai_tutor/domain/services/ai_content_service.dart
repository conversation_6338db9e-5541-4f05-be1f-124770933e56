import 'dart:convert';
import 'dart:developer' as developer;
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../entities/flashcard.dart';
import '../entities/quiz.dart';
import '../entities/learning_progress.dart';
import '../repositories/ai_tutor_repository.dart';

/// Service for generating AI content using LangChain
class AIContentService {
  late final ChatOpenAI _chatModel;
  late final PromptTemplate _learningPlanTemplate;
  late final PromptTemplate _flashcardTemplate;
  late final PromptTemplate _quizTemplate;
  late final PromptTemplate _explanationTemplate;

  AIContentService({String? apiKey}) {
    // Get API key from dotenv environment or use provided key
    final effectiveApiKey =
        apiKey ??
        dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment('OPENAI_API_KEY', defaultValue: '');

    if (effectiveApiKey.isEmpty) {
      throw Exception(
        'OpenAI API key is required. Set OPENAI_API_KEY environment variable or provide apiKey parameter.',
      );
    }

    _chatModel = ChatOpenAI(
      apiKey: effectiveApiKey,
      defaultOptions: const ChatOpenAIOptions(
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000,
      ),
    );

    _initializePromptTemplates();
  }

  /// Initializes prompt templates for different AI content generation tasks
  void _initializePromptTemplates() {
    _learningPlanTemplate = PromptTemplate.fromTemplate('''
You are an expert educational AI tutor. Create a comprehensive learning plan for the following:

Subject: {subject}
Current Level: {currentLevel}
Learning Goals: {learningGoals}
Preferences: {preferences}

Generate a structured learning plan with:
1. Clear milestones (3-5 milestones)
2. Estimated timeframes
3. Learning resources
4. Assessment methods
5. Prerequisites for each milestone

Format the response as JSON with the following structure:
{{
  "title": "Learning Plan Title",
  "description": "Brief description",
  "milestones": [
    {{
      "title": "Milestone Title",
      "description": "Detailed description",
      "concepts": ["concept1", "concept2"],
      "estimatedDays": 14,
      "resources": ["resource1", "resource2"]
    }}
  ],
  "totalDuration": 60,
  "difficulty": "intermediate"
}}
''');

    _flashcardTemplate = PromptTemplate.fromTemplate('''
You are an expert educational content creator. Generate {count} flashcards for the topic: {topic}

Difficulty Level: {difficulty}
Context: {context}

Create flashcards that:
1. Test key concepts and understanding
2. Use clear, concise questions
3. Provide comprehensive answers
4. Include practical examples where relevant
5. Progress from basic to advanced concepts

Format each flashcard as JSON:
{{
  "front": "Question or prompt",
  "back": "Detailed answer with explanation",
  "tags": ["tag1", "tag2"],
  "difficulty": "{difficulty}"
}}

Return an array of {count} flashcards in JSON format.
''');

    _quizTemplate = PromptTemplate.fromTemplate('''
You are an expert quiz creator. Generate an adaptive quiz for:

Topic: {topic}
Concepts: {concepts}
Difficulty Level: {difficulty}
Number of Questions: {questionCount}

Create questions that:
1. Test understanding of key concepts
2. Include multiple choice, true/false, and short answer types
3. Provide clear explanations for correct answers
4. Are appropriate for the specified difficulty level
5. Build upon each other progressively

Format as JSON:
{{
  "title": "Quiz Title",
  "questions": [
    {{
      "question": "Question text",
      "type": "multiple_choice",
      "options": ["A", "B", "C", "D"],
      "correctAnswers": ["A"],
      "explanation": "Why this is correct",
      "concept": "Related concept",
      "points": 10
    }}
  ]
}}
''');

    _explanationTemplate = PromptTemplate.fromTemplate('''
You are an expert tutor. Explain the concept: {concept}

Context: {context}
Explanation Style: {style}

Provide an explanation that:
1. Is appropriate for the specified style
2. Uses clear, accessible language
3. Includes relevant examples
4. Connects to real-world applications
5. Builds understanding progressively

Style Guidelines:
- Simple: Use basic language, avoid jargon
- Detailed: Comprehensive explanation with technical details
- Analogy: Use real-world comparisons and metaphors
- Step-by-Step: Break down into clear sequential steps
- Visual: Describe visual representations and diagrams

Provide a clear, engaging explanation.
''');
  }

  /// Generates a learning plan using AI
  Future<LearningPlan> generateLearningPlan({
    required String subject,
    required String currentLevel,
    required List<String> learningGoals,
    required Map<String, dynamic> preferences,
  }) async {
    try {
      final prompt = _learningPlanTemplate.format({
        'subject': subject,
        'currentLevel': currentLevel,
        'learningGoals': learningGoals.join(', '),
        'preferences': preferences.toString(),
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // Extract content from AI response
      final content = response.output.content;

      // Parse structured JSON from AI response for production use
      try {
        final jsonResponse = _extractJsonFromResponse(content);
        if (jsonResponse != null && jsonResponse is Map<String, dynamic>) {
          return _createLearningPlanFromJson(
            jsonResponse,
            subject,
            currentLevel,
            learningGoals,
          );
        }
      } catch (parseError) {
        developer.log(
          'Failed to parse AI response as JSON: $parseError',
          name: 'AIContentService',
          error: parseError,
        );
      }

      // Fallback to enhanced learning plan with AI content
      return _createEnhancedLearningPlan(
        subject,
        currentLevel,
        learningGoals,
        content,
      );
    } catch (e) {
      // Implement proper error logging and user notification
      developer.log(
        'Error generating learning plan: $e',
        name: 'AIContentService',
        error: e,
      );
      // Return a basic learning plan as fallback
      return _createMockLearningPlan(subject, currentLevel, learningGoals);
    }
  }

  /// Generates flashcards using AI
  Future<List<Flashcard>> generateFlashcards({
    required String topic,
    required int count,
    required DifficultyLevel difficulty,
    String? context,
  }) async {
    try {
      final prompt = _flashcardTemplate.format({
        'topic': topic,
        'count': count.toString(),
        'difficulty': difficulty.displayName.toLowerCase(),
        'context': context ?? 'General learning context',
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // Parse structured JSON from AI response to create proper Flashcard objects
      final content = response.output.content;

      // Log the AI response for debugging
      developer.log(
        'AI Flashcard Response: $content',
        name: 'AIContentService',
      );

      try {
        final jsonResponse = _extractJsonFromResponse(content);
        if (jsonResponse != null) {
          List<dynamic>? flashcardsJson;

          // Check if response is directly an array of flashcards
          if (jsonResponse is List) {
            flashcardsJson = jsonResponse;
          } else if (jsonResponse is Map<String, dynamic> &&
              jsonResponse.containsKey('flashcards')) {
            flashcardsJson = jsonResponse['flashcards'] as List<dynamic>?;
          }

          if (flashcardsJson != null) {
            return _createFlashcardsFromJson(flashcardsJson, topic, difficulty);
          }
        }
      } catch (parseError) {
        developer.log(
          'Failed to parse flashcard JSON: $parseError',
          name: 'AIContentService',
          error: parseError,
        );
      }

      return _createEnhancedFlashcards(topic, count, difficulty, content);
    } catch (e) {
      // Implement proper error handling and logging
      developer.log(
        'Error generating flashcards: $e',
        name: 'AIContentService',
        error: e,
      );
      // Return fallback mock flashcards if AI fails - using mock data
      return _createMockFlashcards(topic, count, difficulty);
    }
  }

  /// Generates a quiz using AI
  Future<Quiz> generateQuiz({
    required String topic,
    required List<String> concepts,
    required DifficultyLevel difficulty,
    int questionCount = 5,
  }) async {
    try {
      final prompt = _quizTemplate.format({
        'topic': topic,
        'concepts': concepts.join(', '),
        'difficulty': difficulty.displayName.toLowerCase(),
        'questionCount': questionCount.toString(),
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // Parse structured JSON from AI response to create proper Quiz object
      final content = response.output.content;

      // Log the AI response for debugging
      developer.log('AI Quiz Response: $content', name: 'AIContentService');

      try {
        final jsonResponse = _extractJsonFromResponse(content);
        if (jsonResponse != null && jsonResponse is Map<String, dynamic>) {
          return _createQuizFromJson(jsonResponse, topic, concepts, difficulty);
        }
      } catch (parseError) {
        developer.log(
          'Failed to parse quiz JSON: $parseError',
          name: 'AIContentService',
          error: parseError,
        );
      }

      return _createEnhancedQuiz(topic, concepts, difficulty, content);
    } catch (e) {
      // Implement proper error handling and logging
      developer.log(
        'Error generating quiz: $e',
        name: 'AIContentService',
        error: e,
      );
      // Return fallback mock quiz if AI fails - using mock data
      return _createMockQuiz(topic, concepts, difficulty);
    }
  }

  /// Explains a concept using AI
  Future<String> explainConcept({
    required String concept,
    required String context,
    required ExplanationStyle style,
  }) async {
    try {
      final prompt = _explanationTemplate.format({
        'concept': concept,
        'context': context,
        'style': style.displayName.toLowerCase(),
      });

      final response = await _chatModel.invoke(PromptValue.string(prompt));

      // Parse and format AI response for better presentation
      final content = response.output.content;

      // Log the AI response for debugging
      developer.log(
        'AI Concept Explanation Response: $content',
        name: 'AIContentService',
      );

      return _createEnhancedExplanation(concept, style, content);
    } catch (e) {
      // Implement proper error handling and logging
      developer.log(
        'Error explaining concept: $e',
        name: 'AIContentService',
        error: e,
      );
      // Return fallback mock explanation if AI fails - using mock data
      return _createMockExplanation(concept, style);
    }
  }

  /// Identifies knowledge gaps using AI analysis
  Future<List<String>> identifyKnowledgeGaps({
    required List<QuizResult> quizResults,
    required String subject,
  }) async {
    try {
      // Implement AI-powered pattern recognition for knowledge gap analysis
      final incorrectConcepts = <String>[];
      final conceptPerformance = <String, List<bool>>{};

      // Collect performance data for each concept
      for (final result in quizResults) {
        for (final answer in result.answers) {
          if (!conceptPerformance.containsKey(answer.concept)) {
            conceptPerformance[answer.concept] = [];
          }
          conceptPerformance[answer.concept]!.add(answer.isCorrect);

          if (!answer.isCorrect) {
            incorrectConcepts.add(answer.concept);
          }
        }
      }

      // Use AI to analyze patterns and suggest specific learning areas
      if (conceptPerformance.isNotEmpty) {
        try {
          final aiAnalysis = await _analyzeKnowledgeGapsWithAI(
            conceptPerformance,
            subject,
          );
          if (aiAnalysis.isNotEmpty) {
            return aiAnalysis;
          }
        } catch (aiError) {
          developer.log(
            'AI knowledge gap analysis failed, using basic analysis: $aiError',
            name: 'AIContentService',
            error: aiError,
          );
        }
      }

      // Enhanced with AI pattern recognition to suggest specific learning areas
      // Basic gap analysis with performance scoring
      final gapAnalysis = <String, double>{};
      for (final entry in conceptPerformance.entries) {
        final concept = entry.key;
        final performances = entry.value;
        final correctCount = performances.where((p) => p).length;
        final accuracy = correctCount / performances.length;

        // Consider concepts with accuracy below 70% as knowledge gaps
        if (accuracy < 0.7) {
          gapAnalysis[concept] = accuracy;
        }
      }

      // Sort by accuracy (worst first) and return concept names
      final sortedGaps = gapAnalysis.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      return sortedGaps.map((e) => e.key).toList();
    } catch (e) {
      // Implement proper error logging and user notification
      developer.log(
        'Error in knowledge gap analysis: $e',
        name: 'AIContentService',
        error: e,
      );
      throw Exception('Failed to identify knowledge gaps: $e');
    }
  }

  /// Uses AI to analyze knowledge gaps and suggest learning areas
  Future<List<String>> _analyzeKnowledgeGapsWithAI(
    Map<String, List<bool>> conceptPerformance,
    String subject,
  ) async {
    final performanceData = conceptPerformance.entries
        .map((entry) {
          final concept = entry.key;
          final performances = entry.value;
          final correctCount = performances.where((p) => p).length;
          final accuracy = (correctCount / performances.length * 100).round();
          return '$concept: $accuracy% accuracy (${performances.length} attempts)';
        })
        .join('\n');

    final prompt =
        '''
Analyze the following learning performance data for $subject and identify the top knowledge gaps that need attention:

Performance Data:
$performanceData

Based on this data, identify the 3-5 most critical knowledge gaps that the student should focus on. Consider:
1. Low accuracy scores
2. Fundamental concepts that affect other areas
3. Prerequisites for advanced topics
4. Common misconceptions

Return only a JSON array of concept names that represent the most important knowledge gaps:
["concept1", "concept2", "concept3"]
''';

    try {
      final response = await _chatModel.invoke(PromptValue.string(prompt));
      final content = response.output.content;

      final jsonResponse = _extractJsonFromResponse(content);
      if (jsonResponse is List) {
        return jsonResponse.map((e) => e.toString()).toList();
      }

      return [];
    } catch (e) {
      developer.log(
        'AI knowledge gap analysis failed: $e',
        name: 'AIContentService',
        error: e,
      );
      return [];
    }
  }

  // Enhanced methods that incorporate AI responses

  /// Creates an enhanced learning plan using AI content
  LearningPlan _createEnhancedLearningPlan(
    String subject,
    String currentLevel,
    List<String> learningGoals,
    String aiContent,
  ) {
    final now = DateTime.now();

    // Extract key information from AI content (simplified approach)
    // In production, you would parse structured JSON from the AI response
    final description = aiContent.isNotEmpty
        ? aiContent.length > 500
              ? '${aiContent.substring(0, 500)}...'
              : aiContent
        : 'A comprehensive learning plan created by AI based on your goals and current level.';

    return LearningPlan(
      id: 'plan_${now.millisecondsSinceEpoch}',
      userId: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      subject: subject,
      title: 'AI-Generated $subject Learning Plan',
      description: description,
      learningGoals: learningGoals,
      difficulty: _parseDifficultyFromLevel(currentLevel),
      startDate: now,
      targetEndDate: now.add(const Duration(days: 30)), // Default 30 days
      milestones: _generateMilestones(subject, learningGoals),
      preferences: {
        'aiGenerated': true,
        'currentLevel': currentLevel,
        'generatedAt': now.toIso8601String(),
      },
      createdAt: now,
      lastUpdated: now,
    );
  }

  /// Helper method to parse difficulty level from string
  DifficultyLevel _parseDifficultyFromLevel(String currentLevel) {
    switch (currentLevel.toLowerCase()) {
      case 'beginner':
      case 'easy':
        return DifficultyLevel.easy;
      case 'intermediate':
      case 'medium':
        return DifficultyLevel.medium;
      case 'advanced':
      case 'hard':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium; // Default to medium
    }
  }

  /// Helper method to generate milestones from learning goals
  List<LearningMilestone> _generateMilestones(
    String subject,
    List<String> learningGoals,
  ) {
    final now = DateTime.now();
    final milestones = <LearningMilestone>[];

    for (int i = 0; i < learningGoals.length; i++) {
      final goal = learningGoals[i];
      final targetDate = now.add(
        Duration(days: (i + 1) * 7),
      ); // Weekly milestones

      milestones.add(
        LearningMilestone(
          id: 'milestone_${now.millisecondsSinceEpoch}_$i',
          title: 'Master: $goal',
          description:
              'Complete understanding and practical application of $goal concepts',
          concepts: [
            goal,
          ], // Simplified - in reality would break down into sub-concepts
          targetDate: targetDate,
          isCompleted: false,
          resources: [
            'Study materials for $goal',
            'Practice exercises',
            'Assessment quiz',
          ],
          metadata: {
            'subject': subject,
            'goalIndex': i,
            'estimatedHours': 10 + (i * 5), // Increasing complexity
          },
        ),
      );
    }

    return milestones;
  }

  // Fallback methods for when AI parsing fails

  /// Creates a mock learning plan as fallback when AI parsing fails
  LearningPlan _createMockLearningPlan(
    String subject,
    String currentLevel,
    List<String> learningGoals,
  ) {
    // Mock learning plan generation - used as fallback when AI parsing fails
    final now = DateTime.now();

    return LearningPlan(
      id: 'plan_${now.millisecondsSinceEpoch}',
      userId: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      subject: subject,
      title: 'AI-Generated $subject Learning Plan',
      description:
          'A comprehensive learning plan created by AI based on your goals and current level.',
      milestones: learningGoals.asMap().entries.map((entry) {
        final index = entry.key;
        final goal = entry.value;

        return LearningMilestone(
          id: 'milestone_${goal.hashCode}',
          title: 'Master $goal',
          description:
              'Complete understanding and practical application of $goal concepts.',
          concepts: [goal],
          targetDate: now.add(Duration(days: (index + 1) * 14)),
          isCompleted: false,
          resources: [
            'AI-Curated Study Materials for $goal',
            'Interactive Exercises: $goal',
            'Practice Problems: $goal',
          ],
          metadata: {
            'aiGenerated': true,
            'difficulty': currentLevel.toLowerCase(),
            'estimatedHours': 10 + (index * 5),
          },
        );
      }).toList(),
      startDate: now,
      targetEndDate: now.add(Duration(days: learningGoals.length * 14)),
      difficulty: _parseDifficultyLevel(currentLevel),
      learningGoals: learningGoals,
      preferences: {},
      createdAt: now,
      lastUpdated: now,
    );
  }

  /// Creates mock flashcards (TODO: Replace with AI parsing)
  List<Flashcard> _createMockFlashcards(
    String topic,
    int count,
    DifficultyLevel difficulty,
  ) {
    // Mock flashcard generation - will be enhanced with AI response parsing
    final now = DateTime.now();

    return List.generate(count, (index) {
      return Flashcard(
        id: 'ai_flashcard_${now.millisecondsSinceEpoch}_$index',
        front: 'AI-Generated Question ${index + 1} about $topic',
        back:
            'AI-Generated comprehensive answer explaining the concept with examples and practical applications.',
        subject:
            'AI-Generated Subject', // Will be extracted from AI response when parsing is implemented
        topic: topic,
        tags: [topic.toLowerCase(), 'ai-generated'],
        difficulty: difficulty,
        createdAt: now,
        lastReviewed: now,
        nextReview: now.add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      );
    });
  }

  /// Creates a mock quiz (TODO: Replace with AI parsing)
  Quiz _createMockQuiz(
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
  ) {
    // Mock quiz generation - will be enhanced with AI response parsing
    final now = DateTime.now();

    final questions = concepts.take(5).map((concept) {
      return QuizQuestion(
        id: 'ai_question_${concept.hashCode}',
        question:
            'AI-Generated question about $concept in the context of $topic?',
        type: QuestionType.multipleChoice,
        options: [
          'AI-Generated Option A for $concept',
          'AI-Generated Option B for $concept',
          'AI-Generated Option C for $concept',
          'AI-Generated Option D for $concept',
        ],
        correctAnswers: ['AI-Generated Option A for $concept'],
        explanation:
            'AI-Generated explanation of why this answer is correct, with detailed reasoning.',
        concept: concept,
        difficulty: difficulty,
        points: 10,
      );
    }).toList();

    return Quiz(
      id: 'ai_quiz_${now.millisecondsSinceEpoch}',
      title: 'AI-Generated Adaptive Quiz: $topic',
      subject:
          'AI-Generated Subject', // Will be extracted from AI response when parsing is implemented
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: now,
      timeLimit: 30,
      isAdaptive: true,
      metadata: {
        'aiGenerated': true,
        'concepts': concepts,
        'generatedAt': now.toIso8601String(),
      },
    );
  }

  /// Creates enhanced flashcards using AI response content
  List<Flashcard> _createEnhancedFlashcards(
    String topic,
    int count,
    DifficultyLevel difficulty,
    String aiContent,
  ) {
    final now = DateTime.now();

    return List.generate(count, (index) {
      return Flashcard(
        id: 'ai_flashcard_${now.millisecondsSinceEpoch}_$index',
        front: 'AI-Enhanced Question ${index + 1} about $topic',
        back:
            'AI-Enhanced answer based on: ${aiContent.length > 100 ? "${aiContent.substring(0, 100)}..." : aiContent}',
        subject: _extractSubjectFromContent(aiContent, topic),
        topic: topic,
        tags: [topic.toLowerCase(), 'ai-enhanced'],
        difficulty: difficulty,
        createdAt: now,
        lastReviewed: now,
        nextReview: now.add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      );
    });
  }

  /// Creates enhanced quiz using AI response content
  Quiz _createEnhancedQuiz(
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
    String aiContent,
  ) {
    final now = DateTime.now();

    final questions = concepts.take(5).map((concept) {
      return QuizQuestion(
        id: 'ai_question_${concept.hashCode}',
        question:
            'AI-Enhanced question about $concept in the context of $topic?',
        type: QuestionType.multipleChoice,
        options: [
          'AI-Enhanced Option A for $concept',
          'AI-Enhanced Option B for $concept',
          'AI-Enhanced Option C for $concept',
          'AI-Enhanced Option D for $concept',
        ],
        correctAnswers: ['AI-Enhanced Option A for $concept'],
        explanation:
            'AI-Enhanced explanation based on: ${aiContent.length > 50 ? "${aiContent.substring(0, 50)}..." : aiContent}',
        concept: concept,
        difficulty: difficulty,
        points: 10,
      );
    }).toList();

    return Quiz(
      id: 'ai_quiz_${now.millisecondsSinceEpoch}',
      title: 'AI-Enhanced Adaptive Quiz: $topic',
      subject: _extractSubjectFromContent(aiContent, topic),
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: now,
      timeLimit: 30,
      isAdaptive: true,
      metadata: {
        'aiGenerated': true,
        'concepts': concepts,
        'generatedAt': now.toIso8601String(),
      },
    );
  }

  /// Extracts subject from AI content or defaults to topic-based subject
  String _extractSubjectFromContent(String content, String topic) {
    // Simple subject extraction - can be enhanced with better parsing
    final lowerContent = content.toLowerCase();
    if (lowerContent.contains('math') ||
        lowerContent.contains('algebra') ||
        lowerContent.contains('calculus')) {
      return 'Mathematics';
    } else if (lowerContent.contains('science') ||
        lowerContent.contains('physics') ||
        lowerContent.contains('chemistry')) {
      return 'Science';
    } else if (lowerContent.contains('history') ||
        lowerContent.contains('historical')) {
      return 'History';
    } else if (lowerContent.contains('language') ||
        lowerContent.contains('literature')) {
      return 'Language Arts';
    }
    return 'General'; // Default fallback
  }

  /// Creates enhanced explanation using AI response content
  String _createEnhancedExplanation(
    String concept,
    ExplanationStyle style,
    String aiContent,
  ) {
    // Use AI content if available, otherwise fallback to mock
    if (aiContent.isNotEmpty && aiContent.length > 10) {
      return 'AI-Enhanced ${style.displayName.toLowerCase()} explanation of $concept:\n\n$aiContent';
    }
    return _createMockExplanation(concept, style);
  }

  /// Creates a mock explanation (Enhanced with AI content awareness)
  String _createMockExplanation(String concept, ExplanationStyle style) {
    return 'AI-Generated ${style.displayName.toLowerCase()} explanation of $concept. '
        'This would contain a comprehensive explanation tailored to the ${style.displayName.toLowerCase()} style, '
        'with appropriate examples, analogies, and level of detail.';
  }

  /// Helper method to parse difficulty level from string
  DifficultyLevel _parseDifficultyLevel(String level) {
    switch (level.toLowerCase()) {
      case 'easy':
      case 'beginner':
        return DifficultyLevel.easy;
      case 'medium':
      case 'intermediate':
        return DifficultyLevel.medium;
      case 'hard':
      case 'advanced':
        return DifficultyLevel.hard;
      case 'expert':
        return DifficultyLevel.expert;
      default:
        return DifficultyLevel.medium;
    }
  }

  // JSON parsing helper methods

  /// Extracts JSON from AI response content
  dynamic _extractJsonFromResponse(String content) {
    try {
      // Try to find JSON in the response (object or array)
      final jsonStart = content.indexOf(RegExp(r'[{\[]'));
      final jsonEnd = content.lastIndexOf(RegExp(r'[}\]]'));

      if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
        final jsonString = content.substring(jsonStart, jsonEnd + 1);
        return json.decode(jsonString);
      }

      // If no JSON found, try to parse the entire content
      return json.decode(content);
    } catch (e) {
      developer.log(
        'Failed to extract JSON from response: $e',
        name: 'AIContentService',
        error: e,
      );
      return null;
    }
  }

  /// Creates a learning plan from parsed JSON response
  LearningPlan _createLearningPlanFromJson(
    Map<String, dynamic> jsonData,
    String subject,
    String currentLevel,
    List<String> learningGoals,
  ) {
    final now = DateTime.now();

    final title =
        jsonData['title'] as String? ?? 'AI-Generated $subject Learning Plan';
    final description =
        jsonData['description'] as String? ??
        'A comprehensive learning plan created by AI based on your goals and current level.';
    final totalDuration = jsonData['totalDuration'] as int? ?? 30;
    final difficultyStr = jsonData['difficulty'] as String? ?? currentLevel;

    // Parse milestones from JSON
    final milestonesJson = jsonData['milestones'] as List<dynamic>? ?? [];
    final milestones = milestonesJson.map((milestoneData) {
      final milestone = milestoneData as Map<String, dynamic>;
      final estimatedDays = milestone['estimatedDays'] as int? ?? 7;

      return LearningMilestone(
        id: 'milestone_${now.millisecondsSinceEpoch}_${milestone.hashCode}',
        title: milestone['title'] as String? ?? 'Learning Milestone',
        description:
            milestone['description'] as String? ?? 'Complete this milestone',
        concepts:
            (milestone['concepts'] as List<dynamic>?)
                ?.map((e) => e.toString())
                .toList() ??
            [],
        targetDate: now.add(Duration(days: estimatedDays)),
        isCompleted: false,
        resources:
            (milestone['resources'] as List<dynamic>?)
                ?.map((e) => e.toString())
                .toList() ??
            [],
        metadata: {'aiGenerated': true, 'estimatedDays': estimatedDays},
      );
    }).toList();

    return LearningPlan(
      id: 'plan_${now.millisecondsSinceEpoch}',
      userId: FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      subject: subject,
      title: title,
      description: description,
      learningGoals: learningGoals,
      difficulty: _parseDifficultyFromLevel(difficultyStr),
      startDate: now,
      targetEndDate: now.add(Duration(days: totalDuration)),
      milestones: milestones,
      preferences: {
        'aiGenerated': true,
        'currentLevel': currentLevel,
        'generatedAt': now.toIso8601String(),
        'parsedFromJson': true,
      },
      createdAt: now,
      lastUpdated: now,
    );
  }

  /// Creates flashcards from parsed JSON response
  List<Flashcard> _createFlashcardsFromJson(
    List<dynamic> flashcardsJson,
    String topic,
    DifficultyLevel difficulty,
  ) {
    final now = DateTime.now();

    return flashcardsJson.asMap().entries.map((entry) {
      final index = entry.key;
      final flashcardData = entry.value as Map<String, dynamic>;

      final front =
          flashcardData['front'] as String? ??
          'AI-Generated Question ${index + 1} about $topic';
      final back =
          flashcardData['back'] as String? ??
          'AI-Generated answer for the question';
      final tags =
          (flashcardData['tags'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [topic.toLowerCase(), 'ai-generated'];
      final cardDifficulty = flashcardData['difficulty'] as String?;

      return Flashcard(
        id: 'ai_flashcard_${now.millisecondsSinceEpoch}_$index',
        front: front,
        back: back,
        subject: _extractSubjectFromContent(back, topic),
        topic: topic,
        tags: tags,
        difficulty: cardDifficulty != null
            ? _parseDifficultyLevel(cardDifficulty)
            : difficulty,
        createdAt: now,
        lastReviewed: now,
        nextReview: now.add(const Duration(days: 1)),
        reviewCount: 0,
        easeFactor: 2.5,
        interval: 1,
      );
    }).toList();
  }

  /// Creates a quiz from parsed JSON response
  Quiz _createQuizFromJson(
    Map<String, dynamic> jsonData,
    String topic,
    List<String> concepts,
    DifficultyLevel difficulty,
  ) {
    final now = DateTime.now();

    final title = jsonData['title'] as String? ?? 'AI-Generated Quiz: $topic';
    final questionsJson = jsonData['questions'] as List<dynamic>? ?? [];

    final questions = questionsJson.map((questionData) {
      final question = questionData as Map<String, dynamic>;

      final questionText =
          question['question'] as String? ?? 'AI-Generated Question';
      final typeStr = question['type'] as String? ?? 'multiple_choice';
      final options =
          (question['options'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [];
      final correctAnswers =
          (question['correctAnswers'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [];
      final explanation =
          question['explanation'] as String? ?? 'AI-Generated explanation';
      final concept =
          question['concept'] as String? ??
          (concepts.isNotEmpty ? concepts.first : topic);
      final points = question['points'] as int? ?? 10;

      QuestionType questionType;
      switch (typeStr.toLowerCase()) {
        case 'true_false':
        case 'trueFalse':
          questionType = QuestionType.trueFalse;
          break;
        case 'fill_in_blank':
        case 'fillInBlank':
          questionType = QuestionType.fillInBlank;
          break;
        case 'short_answer':
        case 'shortAnswer':
          questionType = QuestionType.shortAnswer;
          break;
        case 'essay':
          questionType = QuestionType.essay;
          break;
        default:
          questionType = QuestionType.multipleChoice;
      }

      return QuizQuestion(
        id: 'ai_question_${now.millisecondsSinceEpoch}_${question.hashCode}',
        question: questionText,
        type: questionType,
        options: options,
        correctAnswers: correctAnswers,
        explanation: explanation,
        concept: concept,
        difficulty: difficulty,
        points: points,
      );
    }).toList();

    return Quiz(
      id: 'ai_quiz_${now.millisecondsSinceEpoch}',
      title: title,
      subject: _extractSubjectFromContent(title, topic),
      topic: topic,
      questions: questions,
      difficulty: difficulty,
      createdAt: now,
      timeLimit: 30,
      isAdaptive: true,
      metadata: {
        'aiGenerated': true,
        'concepts': concepts,
        'generatedAt': now.toIso8601String(),
        'parsedFromJson': true,
      },
    );
  }
}
